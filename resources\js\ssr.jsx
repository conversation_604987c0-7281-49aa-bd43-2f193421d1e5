import { createInertiaApp } from "@inertiajs/react";
import { renderToString } from "react-dom/server";
import { route } from "ziggy-js";
import Layout from "./Layout";

export default function render(page) {
    return createInertiaApp({
        page,
        render: renderToString,
        resolve: (name) => {
            const pages = import.meta.glob("./pages/**/*.jsx", { eager: true });
            const page = pages[`./pages/${name}.jsx`];
            page.default.layout =
                page.default.layout || ((page) => <Layout>{page}</Layout>);
            return page;
        },
        setup: ({ App, props }) => {
            // Make Ziggy routes available globally for SSR
            if (typeof global !== "undefined") {
                global.route = route;
                global.Ziggy = props.initialPage.props.ziggy;
            }
            return <App {...props} />;
        },
    });
}
